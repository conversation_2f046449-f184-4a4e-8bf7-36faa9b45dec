using EnvizonController.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Domain.Aggregates
{
    /// <summary>
    /// 协议项类
    /// 表示协议中的一个数据项
    /// </summary>
    public class ProtocolItem
    {
        /// <summary>
        /// 唯一标识符
        /// </summary>
        public required int Index { get; set; }
        /// <summary>
        /// 基本信息
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;
        
        /// <summary>
        /// 设备通信相关
        /// </summary>
        public byte UnitId { get; set; }
        
        /// <summary>
        /// 功能码
        /// </summary>
        public byte FunctionCode { get; set; }
        
        /// <summary>
        /// 地址
        /// </summary>
        public ushort Address { get; set; }
        
        /// <summary>
        /// 寄存器数量
        /// </summary>
        public ushort RegisterCount { get; set; }
        
        /// <summary>
        /// 数据类型
        /// </summary>
        public DataType DataType { get; set; }

        /// <summary>
        /// 用户界面分组
        /// </summary>
        public string GroupName { get; set; } = string.Empty;

        /// <summary>
        /// 列名
        /// </summary>
        public string ColumnName { get; set; } = string.Empty;

        /// <summary>
        /// 报警阈值
        /// 最大值
        /// </summary>
        public double? MaxValue { get; set; }
        
        /// <summary>
        /// 最小值
        /// </summary>
        public double? MinValue { get; set; }
        
        /// <summary>
        /// 报警严重级别
        /// </summary>
        public AlarmSeverity AlarmSeverity { get; set; }

        /// <summary>
        /// 图表中新增字段
        /// </summary>
        public string Location { get; set; } = string.Empty;
        
        /// <summary>
        /// 类型
        /// </summary>
        public string Type { get; set; } = string.Empty;
        
        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否常开
        /// </summary>
        public bool IsNormallyOpen { get; set; }
        
        /// <summary>
        /// 故障字典
        /// </summary>
        public string TroubleCodeDictionary { get; set; } = string.Empty;
        
        /// <summary>
        /// 关联动作
        /// </summary>
        public string RelatedAction { get; set; } = string.Empty;

        /// <summary>
        /// 是否有报警阈值
        /// </summary>
        public bool HasAlarmThresholds => MaxValue.HasValue || MinValue.HasValue;

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 缩放因子
        /// </summary>
        public double ScaleFactor { get; set; } = 1.0;

        /// <summary>
        /// 偏移量
        /// </summary>
        public double Offset { get; set; } = 0.0;

        /// <summary>
        /// 是否可写
        /// </summary>
        public bool IsWritable { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        ///  格式如 原始值1:含义1;原始值2:含义2;...。例如 1:启动;2:暂停;3:停止。
        /// </summary>

        public Dictionary<int, string>? ValueMappings { get; set; } 
        /// <summary>
        /// 格式如 位索引0:含义0;位索引1:含义1;...。例如 0:运行状态;1:故障标志;7:模式A。
        /// </summary>

        public Dictionary<int, string>? BitMappings { get; set; } 
        /// <summary>
        /// 字序类型（适用于32位及以上数据类型）
        /// </summary>
        public WordOrderType WordOrderType { get; set; } = WordOrderType.BigEndian;

        public Dictionary<string, int>? GetReverseValueMappings()
        {
            return ValueMappings?.ToDictionary(kvp => kvp.Value, kvp => kvp.Key);
        }
    }
}
