using EnvizonController.Domain.Aggregates;
using EnvizonController.Application.DeviceCommands.Models;

namespace EnvizonController.Application.DeviceCommands.Interfaces
{
    /// <summary>
    /// 协议智能解析器接口
    /// </summary>
    public interface IProtocolCommandResolver
    {
        /// <summary>
        /// 解析指令到协议项
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="commandName">指令名称</param>
        /// <returns>匹配的协议项</returns>
        Task<ProtocolItem?> ResolveCommandToProtocolItemAsync(long deviceId, string commandName);
        
        /// <summary>
        /// 根据名称查找协议项
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="protocolItemName">协议项名称</param>
        /// <returns>匹配的协议项</returns>
        Task<ProtocolItem?> FindProtocolItemByNameAsync(long deviceId, string protocolItemName);
        
        /// <summary>
        /// 智能匹配协议项（支持中英文、别名匹配）
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="searchTerm">搜索词</param>
        /// <returns>匹配的协议项</returns>
        Task<ProtocolItem?> SmartMatchProtocolItemAsync(long deviceId, string searchTerm);
        
        /// <summary>
        /// 获取设备支持的所有指令
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>支持的指令列表</returns>
        Task<IEnumerable<CommandInfo>> GetSupportedCommandsAsync(long deviceId);
        
        /// <summary>
        /// 验证指令参数
        /// </summary>
        /// <param name="protocolItem">协议项</param>
        /// <param name="parameters">参数</param>
        /// <returns>验证结果</returns>
        Task<ValidationResult> ValidateCommandParametersAsync(ProtocolItem protocolItem, object? parameters);
        
        /// <summary>
        /// 获取设备的协议项列表
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>协议项列表</returns>
        Task<List<ProtocolItem>> GetDeviceProtocolItemsAsync(long deviceId);
        
        /// <summary>
        /// 获取推荐的协议项
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="searchTerm">搜索词</param>
        /// <param name="maxRecommendations">最大推荐数量</param>
        /// <returns>推荐的协议项列表</returns>
        Task<List<ProtocolItem>> GetRecommendedProtocolItemsAsync(long deviceId, string searchTerm, int maxRecommendations = 5);
    }
} 