using System.Text.RegularExpressions;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Application.DeviceCommands.Configuration;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Application.DeviceCommands.SmartMatching
{
    /// <summary>
    /// 智能匹配引擎
    /// </summary>
    public class SmartMatchingEngine
    {
        private readonly ILogger<SmartMatchingEngine> _logger;
        private readonly CommandMappingConfig _config;

        public SmartMatchingEngine(ILogger<SmartMatchingEngine> logger, CommandMappingConfig config)
        {
            _logger = logger;
            _config = config;
        }

        /// <summary>
        /// 基于模糊匹配的协议项查找
        /// </summary>
        /// <param name="protocolItems">协议项列表</param>
        /// <param name="searchTerm">搜索词</param>
        /// <returns>匹配的协议项</returns>
        public async Task<ProtocolItem?> FuzzyMatchProtocolItemAsync(
            List<ProtocolItem> protocolItems, string searchTerm)
        {
            if (protocolItems == null || !protocolItems.Any() || string.IsNullOrWhiteSpace(searchTerm))
                return null;

            _logger.LogDebug("开始模糊匹配协议项，搜索词: {SearchTerm}", searchTerm);

            // 1. 精确匹配 (Name, DisplayName)
            var exactMatch = protocolItems.FirstOrDefault(p => 
                string.Equals(p.Name, searchTerm, StringComparison.OrdinalIgnoreCase) ||
                string.Equals(p.DisplayName, searchTerm, StringComparison.OrdinalIgnoreCase));
            
            if (exactMatch != null)
            {
                _logger.LogDebug("找到精确匹配: {ItemName}", exactMatch.Name);
                return exactMatch;
            }

            // 2. 别名匹配（基于预定义配置）
            var aliasMatch = await MatchByAliasAsync(protocolItems, searchTerm);
            if (aliasMatch != null)
            {
                _logger.LogDebug("找到别名匹配: {ItemName}", aliasMatch.Name);
                return aliasMatch;
            }

            // 3. 智能规则匹配
            var ruleMatch = await MatchByRulesAsync(protocolItems, searchTerm);
            if (ruleMatch != null)
            {
                _logger.LogDebug("找到规则匹配: {ItemName}", ruleMatch.Name);
                return ruleMatch;
            }

            // 4. 包含匹配
            var containsMatch = protocolItems.FirstOrDefault(p =>
                p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                p.DisplayName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
            
            if (containsMatch != null)
            {
                _logger.LogDebug("找到包含匹配: {ItemName}", containsMatch.Name);
                return containsMatch;
            }

            // 5. 相似度匹配 (Levenshtein距离)
            var similarityMatches = protocolItems
                .Select(p => new { 
                    Item = p, 
                    Similarity = Math.Max(
                        CalculateSimilarity(p.Name, searchTerm),
                        CalculateSimilarity(p.DisplayName, searchTerm)
                    )
                })
                .Where(x => x.Similarity > 0.7)
                .OrderByDescending(x => x.Similarity)
                .FirstOrDefault();
            
            if (similarityMatches != null)
            {
                _logger.LogDebug("找到相似度匹配: {ItemName}, 相似度: {Similarity}", 
                    similarityMatches.Item.Name, similarityMatches.Similarity);
                return similarityMatches.Item;
            }

            _logger.LogDebug("未找到匹配的协议项，搜索词: {SearchTerm}", searchTerm);
            return null;
        }

        /// <summary>
        /// 基于别名匹配
        /// </summary>
        private async Task<ProtocolItem?> MatchByAliasAsync(List<ProtocolItem> protocolItems, string searchTerm)
        {
            // 检查预定义指令的别名
            foreach (var predefinedCommand in _config.PredefinedCommands)
            {
                if (predefinedCommand.Value.Aliases.Contains(searchTerm, StringComparer.OrdinalIgnoreCase))
                {
                    var targetItem = protocolItems.FirstOrDefault(p =>
                        string.Equals(p.Name, predefinedCommand.Value.ProtocolItemName, StringComparison.OrdinalIgnoreCase) ||
                        string.Equals(p.DisplayName, predefinedCommand.Value.ProtocolItemName, StringComparison.OrdinalIgnoreCase));
                    
                    if (targetItem != null)
                        return targetItem;
                }
            }

            return await Task.FromResult<ProtocolItem?>(null);
        }

        /// <summary>
        /// 基于智能规则匹配
        /// </summary>
        private async Task<ProtocolItem?> MatchByRulesAsync(List<ProtocolItem> protocolItems, string searchTerm)
        {
            var applicableRules = _config.SmartMatchingRules
                .Where(rule => Regex.IsMatch(searchTerm, rule.Pattern, RegexOptions.IgnoreCase))
                .OrderBy(rule => rule.Priority)
                .ToList();

            foreach (var rule in applicableRules)
            {
                var targetItem = protocolItems.FirstOrDefault(p =>
                    string.Equals(p.Name, rule.TargetProtocolItemName, StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(p.DisplayName, rule.TargetProtocolItemName, StringComparison.OrdinalIgnoreCase));
                
                if (targetItem != null)
                    return targetItem;
            }

            return await Task.FromResult<ProtocolItem?>(null);
        }

        /// <summary>
        /// 计算字符串相似度（基于Levenshtein距离）
        /// </summary>
        /// <param name="source">源字符串</param>
        /// <param name="target">目标字符串</param>
        /// <returns>相似度值（0-1）</returns>
        private double CalculateSimilarity(string source, string target)
        {
            if (string.IsNullOrEmpty(source) || string.IsNullOrEmpty(target))
                return 0;

            if (source == target)
                return 1;

            int distance = LevenshteinDistance(source.ToLower(), target.ToLower());
            int maxLength = Math.Max(source.Length, target.Length);
            
            return 1.0 - (double)distance / maxLength;
        }

        /// <summary>
        /// 计算Levenshtein距离
        /// </summary>
        private int LevenshteinDistance(string source, string target)
        {
            if (source.Length == 0) return target.Length;
            if (target.Length == 0) return source.Length;

            int[,] matrix = new int[source.Length + 1, target.Length + 1];

            // 初始化第一行和第一列
            for (int i = 0; i <= source.Length; i++)
                matrix[i, 0] = i;
            for (int j = 0; j <= target.Length; j++)
                matrix[0, j] = j;

            // 填充矩阵
            for (int i = 1; i <= source.Length; i++)
            {
                for (int j = 1; j <= target.Length; j++)
                {
                    int cost = source[i - 1] == target[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[source.Length, target.Length];
        }

        /// <summary>
        /// 获取推荐的协议项
        /// </summary>
        /// <param name="protocolItems">协议项列表</param>
        /// <param name="searchTerm">搜索词</param>
        /// <param name="maxRecommendations">最大推荐数量</param>
        /// <returns>推荐的协议项列表</returns>
        public async Task<List<ProtocolItem>> GetRecommendationsAsync(
            List<ProtocolItem> protocolItems, string searchTerm, int maxRecommendations = 5)
        {
            if (protocolItems == null || !protocolItems.Any() || string.IsNullOrWhiteSpace(searchTerm))
                return new List<ProtocolItem>();

            var recommendations = protocolItems
                .Select(p => new { 
                    Item = p, 
                    Similarity = Math.Max(
                        CalculateSimilarity(p.Name, searchTerm),
                        CalculateSimilarity(p.DisplayName, searchTerm)
                    )
                })
                .Where(x => x.Similarity > 0.3)
                .OrderByDescending(x => x.Similarity)
                .Take(maxRecommendations)
                .Select(x => x.Item)
                .ToList();

            return await Task.FromResult(recommendations);
        }
    }
} 