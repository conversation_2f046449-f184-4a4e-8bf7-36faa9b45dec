using System.ComponentModel;
using Avalonia.Media;
using CommunityToolkit.Mvvm.DependencyInjection;
using EnvizonController.Presentation.ViewModels;
using EnvizonController.Presentation.ViewModels.Program;
using EnvizonController.Shared.DTOs;
using HanumanInstitute.MvvmDialogs;
using HanumanInstitute.MvvmDialogs.Avalonia.DialogHost;
using HanumanInstitute.MvvmDialogs.FileSystem;
using HanumanInstitute.MvvmDialogs.FrameworkDialogs;

namespace EnvizonController.Presentation.Extensions
{
    public static class DialogServiceExtensions
    {
        /// <summary>
        /// 显示新增程式表对话框
        /// </summary>
        /// <param name="service">对话框服务</param>
        /// <param name="ownerViewModel">所有者视图模型</param>
        /// <returns>如果用户保存了程式表，则返回程式表视图模型；否则返回null</returns>
        /// <remarks>
        /// 此方法用于创建新的程式表，Id默认为0，触发创建操作
        /// </remarks>
        public static async Task<ProgramLinkItemViewModel?> AddProgramLinkAsync(this IDialogService service, INotifyPropertyChanged ownerViewModel)
        {
            var vm = service.CreateViewModel<ProgramLinkItemViewModel>();
            var settings = new DialogHostSettings(vm);
            await service.ShowDialogHostAsync(Ioc.Default.GetService<MainViewModel>()!, settings).ConfigureAwait(true);
            return vm.DialogResult == true ? vm : null;
        }
        /// <summary>
        /// 显示新增程式表对话框
        /// </summary>
        /// <param name="service">对话框服务</param>
        /// <param name="ownerViewModel">所有者视图模型</param>
        /// <returns>如果用户保存了程式表，则返回程式表视图模型；否则返回null</returns>
        /// <remarks>
        /// 此方法用于创建新的程式表，Id默认为0，触发创建操作
        /// </remarks>
        public static async Task<ProgramItemViewModel?> AddProgramAsync(this IDialogService service, INotifyPropertyChanged ownerViewModel)
        {
            var vm = service.CreateViewModel<ProgramItemViewModel>();
            var settings = new DialogHostSettings(vm);
            await service.ShowDialogHostAsync(Ioc.Default.GetService<MainViewModel>()!, settings).ConfigureAwait(true);
            return vm.DialogResult == true ? vm : null;
        }

        /// <summary>
        /// 显示编辑程式表对话框
        /// </summary>
        /// <param name="service">对话框服务</param>
        /// <param name="ownerViewModel">所有者视图模型</param>
        /// <param name="programToEdit">要编辑的程式表视图模型</param>
        /// <returns>如果用户保存了程式表，则返回程式表视图模型；否则返回null</returns>
        /// <remarks>
        /// 此方法用于编辑现有程式表，会保留原Id，触发更新操作
        /// </remarks>
        public static async Task<ProgramItemViewModel?> EditProgramAsync(this IDialogService service, INotifyPropertyChanged ownerViewModel, ProgramItemViewModel programToEdit)
        {
            var vm = service.CreateViewModel<ProgramItemViewModel>();
            // Copy properties from the program to edit to the view model
            vm.Id = programToEdit.Id;
            vm.Name = programToEdit.Name;
            vm.CycleCount = programToEdit.CycleCount;
            vm.CycleStart = programToEdit.CycleStart;
            vm.CycleEnd = programToEdit.CycleEnd;
            vm.CreatedAt = programToEdit.CreatedAt;
            vm.UpdatedAt = programToEdit.UpdatedAt;
            
            // Copy steps if any
            foreach (var step in programToEdit.Steps)
            {
                vm.Steps.Add(step);
            }
            
            var settings = new DialogHostSettings(vm);
            await service.ShowDialogHostAsync(Ioc.Default.GetService<MainViewModel>()!, settings).ConfigureAwait(true);
            return vm.DialogResult == true ? vm : null;
        }

        /// <summary>
        /// 显示编辑程式链接对话框
        /// </summary>
        /// <param name="service">对话框服务</param>
        /// <param name="ownerViewModel">所有者视图模型</param>
        /// <param name="programLinkToEdit">要编辑的程式链接视图模型</param>
        /// <returns>如果用户保存了程式链接，则返回程式链接视图模型；否则返回null</returns>
        /// <remarks>
        /// 此方法用于编辑现有程式链接，会保留原Id，触发更新操作
        /// </remarks>
        public static async Task<ProgramLinkItemViewModel?> EditProgramLinkAsync(this IDialogService service, INotifyPropertyChanged ownerViewModel, ProgramLinkItemViewModel programLinkToEdit)
        {
            var vm = service.CreateViewModel<ProgramLinkItemViewModel>();
            // 从要编辑的程式链接复制属性到视图模型
            vm.Id = programLinkToEdit.Id;
            vm.Name = programLinkToEdit.Name;
            vm.CycleCount = programLinkToEdit.CycleCount;
            vm.CreatedAt = programLinkToEdit.CreatedAt;
            vm.UpdatedAt = programLinkToEdit.UpdatedAt;
            
            // 复制链接项（如果有）
            foreach (var item in programLinkToEdit.Items)
            {
                vm.Items.Add(item);
            }
            
            var settings = new DialogHostSettings(vm);
            await service.ShowDialogHostAsync(Ioc.Default.GetService<MainViewModel>()!, settings).ConfigureAwait(true);
            return vm.DialogResult == true ? vm : null;
        }
        
        /// <summary>
        /// Shows a message box with the main window as the owner, automatically resolved from the IoC container.
        /// </summary>
        /// <param name="dialogService">The dialog service instance.</param>
        /// <param name="text">The text to display in the message box.</param>
        /// <param name="title">The title of the message box.</param>
        /// <param name="buttons">The buttons to display in the message box. Default is OK button.</param>
        /// <param name="icon">The icon to display in the message box. Default is no icon.</param>
        /// <returns>A task that completes when the message box is closed, containing the user's response.</returns>
        public static Task<bool?> ShowMessageBoxAsync(
            this IDialogService dialogService,
            string text,
            string title = "",
            MessageBoxButton buttons = MessageBoxButton.Ok,
            MessageBoxImage icon = MessageBoxImage.None)
        {
            var owner = Ioc.Default.GetService<MainViewModel>();
            return dialogService.ShowMessageBoxAsync(owner, text, title, buttons, icon);
        }

        /// <summary>
        /// Shows a dialog with the main window as the owner, automatically resolved from the IoC container.
        /// </summary>
        /// <typeparam name="T">The return type of the dialog.</typeparam>
        /// <param name="dialogService">The dialog service instance.</param>
        /// <param name="viewModel">The view model of the dialog to show.</param>
        /// <returns>The dialog result.</returns>
        public static Task<bool?> ShowDialogAsync<T>(
            this IDialogService dialogService,
            IModalDialogViewModel viewModel)
        {
            var owner = Ioc.Default.GetService<MainViewModel>();
            return dialogService.ShowDialogAsync<T>(owner, viewModel);
        }

        /// <summary>
        /// Shows an open file dialog with the main window as the owner, automatically resolved from the IoC container.
        /// </summary>
        /// <param name="dialogService">The dialog service instance.</param>
        /// <param name="settings">The settings for the open file dialog.</param>
        /// <returns>An array of selected file paths, or null if the dialog was canceled.</returns>
        public static Task<IDialogStorageFile  ?> ShowOpenFileDialogAsync(
            this IDialogService dialogService,
            OpenFileDialogSettings? settings = null)
        {
            var owner = Ioc.Default.GetService<MainViewModel>();
            return dialogService.ShowOpenFileDialogAsync(owner, settings);
        }

        /// <summary>
        /// Shows a save file dialog with the main window as the owner, automatically resolved from the IoC container.
        /// </summary>
        /// <param name="dialogService">The dialog service instance.</param>
        /// <param name="settings">The settings for the save file dialog.</param>
        /// <returns>The selected file path, or null if the dialog was canceled.</returns>
        public static Task<IDialogStorageFile?> ShowSaveFileDialogAsync(
            this IDialogService dialogService,
            SaveFileDialogSettings? settings = null)
        {
            var owner = Ioc.Default.GetService<MainViewModel>();
            return dialogService.ShowSaveFileDialogAsync(owner, settings);
        }

        /// <summary>
        /// Shows a folder browser dialog with the main window as the owner, automatically resolved from the IoC container.
        /// </summary>
        /// <param name="dialogService">The dialog service instance.</param>
        /// <param name="settings">The settings for the folder browser dialog.</param>
        /// <returns>The selected directory path, or null if the dialog was canceled.</returns>
        public static Task<IDialogStorageFolder?> ShowOpenFolderDialogAsync(
            this IDialogService dialogService,
            OpenFolderDialogSettings? settings = null)
        {
            var owner = Ioc.Default.GetService<MainViewModel>();
            return dialogService.ShowOpenFolderDialogAsync(owner, settings);
        }

        /// <summary>
        /// 显示错误消息对话框，自动以主窗口为 owner。
        /// </summary>
        /// <param name="dialogService">对话框服务实例</param>
        /// <param name="title">对话框标题</param>
        /// <param name="message">错误消息内容</param>
        /// <returns>Task</returns>
        public static async Task ShowErrorAsync(
            this IDialogService dialogService,
            string title,
            string message)
        {
            var owner = CommunityToolkit.Mvvm.DependencyInjection.Ioc.Default.GetService<MainViewModel>();
            await dialogService.ShowMessageBoxAsync(
                owner,
                message,
                title,
                MessageBoxButton.Ok,
                MessageBoxImage.Error
            );
        }

        /// <summary>
        /// 显示测试启动对话框
        /// </summary>
        /// <param name="service">对话框服务</param>
        /// <param name="ownerViewModel">所有者视图模型</param>
        /// <returns>如果用户确认启动，则返回测试启动信息；否则返回null</returns>
        public static async Task<TestStartDialogViewModel?> ShowTestStartDialogAsync(this IDialogService service, INotifyPropertyChanged ownerViewModel)
        {
            var vm = service.CreateViewModel<TestStartDialogViewModel>();
            var settings = new DialogHostSettings(vm);
            await service.ShowDialogHostAsync(Ioc.Default.GetService<MainViewModel>()!, settings).ConfigureAwait(true);
            return vm.DialogResult == true ? vm : null;
        }
    }
}
