using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HanumanInstitute.MvvmDialogs;

namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// 测试启动对话框视图模型
    /// </summary>
    public partial class TestStartDialogViewModel : ViewModelBase, IModalDialogViewModel, ICloseable
    {
        /// <summary>
        /// 测试名称
        /// </summary>
        [ObservableProperty]
        private string _testName = string.Empty;

        /// <summary>
        /// 测试备注
        /// </summary>
        [ObservableProperty]
        private string? _testRemark = null;

        /// <summary>
        /// 对话框结果
        /// </summary>
        public bool? DialogResult { get; private set; }

        /// <summary>
        /// 确认启动命令
        /// </summary>
        [RelayCommand]
        private void Confirm()
        {
            if (string.IsNullOrWhiteSpace(TestName))
            {
                // 可以添加验证逻辑
                return;
            }
            
            DialogResult = true;
            OnPropertyChanged(nameof(DialogResult));
            RequestClose?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 取消命令
        /// </summary>
        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            OnPropertyChanged(nameof(DialogResult));

            RequestClose?.Invoke(this, EventArgs.Empty);
        }

        public event EventHandler? RequestClose;
    }
} 